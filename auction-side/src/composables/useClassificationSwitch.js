import { ref, computed } from 'vue'
import { CLASSIFICATIONS } from '@/defined/const'

/**
 * Composable for managing classification switching between ascending and sealed auction types
 * @param {string} defaultClassification - Default classification to start with (optional)
 * @returns {object} Classification state and methods
 */
export default function useClassificationSwitch(defaultClassification = CLASSIFICATIONS.ASCENDING) {
  // Reactive classification state
  const classification = ref(defaultClassification)

  // Computed properties for easy checking
  const isAscending = computed(() => classification.value === CLASSIFICATIONS.ASCENDING)
  const isSealed = computed(() => classification.value === CLASSIFICATIONS.SEALED)

  // Methods for switching classification
  const switchToAscending = () => {
    classification.value = CLASSIFICATIONS.ASCENDING
  }

  const switchToSealed = () => {
    classification.value = CLASSIFICATIONS.SEALED
  }

  const toggleClassification = () => {
    classification.value = isAscending.value ? CLASSIFICATIONS.SEALED : CLASSIFICATIONS.ASCENDING
  }

  const setClassification = (newClassification) => {
    if (newClassification === CLASSIFICATIONS.ASCENDING || newClassification === CLASSIFICATIONS.SEALED) {
      classification.value = newClassification
    }
  }

  // Get the appropriate component name based on classification
  const getComponentName = (baseName) => {
    return isAscending.value ? `${baseName}Ascending` : `${baseName}Sealed`
  }

  // Get classification display labels
  const getClassificationLabel = (classificationType) => {
    return classificationType === CLASSIFICATIONS.ASCENDING ? '競り上がり入札' : '封印入札'
  }

  const currentLabel = computed(() => getClassificationLabel(classification.value))

  return {
    // State
    classification,
    isAscending,
    isSealed,
    currentLabel,
    
    // Methods
    switchToAscending,
    switchToSealed,
    toggleClassification,
    setClassification,
    getComponentName,
    getClassificationLabel,
    
    // Constants for convenience
    CLASSIFICATIONS
  }
}
