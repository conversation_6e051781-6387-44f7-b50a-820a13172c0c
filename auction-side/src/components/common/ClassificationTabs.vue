<script setup lang="ts">
import { CLASSIFICATIONS } from '@/defined/const'

// Props
interface Props {
  modelValue: string
  disabled?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  disabled: false
})

// Emits
const emit = defineEmits<{
  'update:modelValue': [value: string]
}>()

// Handle tab click
const handleTabClick = (classification: string) => {
  if (!props.disabled && props.modelValue !== classification) {
    emit('update:modelValue', classification)
  }
}

// Check if tab is active
const isActive = (classification: string) => {
  return props.modelValue === classification
}
</script>

<template>
  <div class="bid-tab-wrap">
    <div 
      class="tab-cont" 
      :class="{ 
        active: isActive(CLASSIFICATIONS.ASCENDING),
        disabled: disabled 
      }"
      @click="handleTabClick(CLASSIFICATIONS.ASCENDING)"
    >
      <div class="label">競り上がり入札</div>
    </div>
    <div 
      class="tab-cont" 
      :class="{ 
        active: isActive(CLASSIFICATIONS.SEALED),
        disabled: disabled 
      }"
      @click="handleTabClick(CLASSIFICATIONS.SEALED)"
    >
      <div class="label">封印入札</div>
    </div>
  </div>
</template>

<style scoped>
.tab-cont {
  cursor: pointer;
  transition: all 0.2s ease;
}

.tab-cont.disabled {
  cursor: not-allowed;
  opacity: 0.6;
}

.tab-cont:not(.disabled):hover {
  opacity: 0.8;
}

.tab-cont.active {
  /* Active styles will be inherited from existing CSS */
}
</style>
